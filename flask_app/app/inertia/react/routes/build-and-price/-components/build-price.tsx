import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { PumpTopForm } from "./1-pump-top/pump-top";
import { useSearch } from "@tanstack/react-router";
import { BuildForm } from "./2-build/build";
import { Options } from "./3-options/options";
import { BuildAndPriceWizard } from "../-store/progress/context";
import { useStepTwoData } from "../-hooks/step-data";

function BuildAndPrice() {
  const { step, build } = useSearch({ from: "/" });
  const { isSuccess, selectedPumpTop, selectedSiteVoltage, selectedPowerUnit } =
    useStepTwoData();
  return (
    <Card className="mar-w-7xl w-full flex-grow bg-white p-4 lg:p-8">
      <CardHeader className="flex flex-col items-center justify-center"></CardHeader>
      <CardContent className="flex h-full flex-col items-center justify-between gap-4 px-0 md:px-6">
        <div className="flex w-full flex-grow flex-col items-center justify-start gap-4">
          {isSuccess ? (
            <BuildAndPriceWizard.Provider
              key={JSON.stringify({ step, build })}
              options={{
                input: {
                  step,
                  build: {
                    productName: build.product_name,
                    pumpTop: selectedPumpTop,
                    siteVoltage: selectedSiteVoltage,
                    powerUnit: selectedPowerUnit,
                  },
                },
              }}
            >
              {step === 1 ? (
                <PumpTopForm />
              ) : step === 2 ? (
                <BuildForm />
              ) : step === 3 ? (
                <Options />
              ) : null}
            </BuildAndPriceWizard.Provider>
          ) : (
            <div className="flex flex-col items-center justify-center gap-4">
              Loading...
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export { BuildAndPrice };
