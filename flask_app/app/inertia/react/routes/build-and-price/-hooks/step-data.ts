import { $api } from "@/api/web-api";
import React from "react";
import { useBuildStore } from "../-store/progress/hooks";
import { useSearch } from "@tanstack/react-router";

const useStep = () => {
  const { step } = useSearch({ from: "/" });
  const store = useBuildStore();
  React.useEffect(() => {
    store.send({ type: "jumpToStep", step });
  }, [step, store]);
};

const useStepOneData = () => {
  const {
    build: { pump_top_id },
  } = useSearch({ from: "/" });
  const enabled = pump_top_id > 0;
  const { data, isSuccess } = $api.useQuery(
    "get",
    "/v1/pricing/pump-top/{part_id}",
    {
      params: { path: { part_id: pump_top_id } },
    },
    {
      enabled,
      select(data) {
        return data.result;
      },
    },
  );
  const store = useBuildStore();
  React.useEffect(() => {
    if (isSuccess) {
      store.send({
        type: "setPumpTop",
        pump_top: data ?? {},
      });
    }
  }, [isSuccess, data, store]);
  return {
    selectedPumpTop: data,
    isSuccess: enabled ? isSuccess : true,
    enabled,
  };
};

const useStepTwoData = () => {
  const {
    selectedPumpTop,
    isSuccess: previousSuccess,
    enabled: previousEnabled,
  } = useStepOneData();
  const {
    build: { site_voltage_id, power_unit_id },
  } = useSearch({ from: "/" });
  const store = useBuildStore();
  const siteVoltageEnabled = site_voltage_id > 0;
  const { data: selectedSiteVoltage, isSuccess: isSuccessSiteVoltage } =
    $api.useQuery(
      "get",
      "/v1/pricing/voltage/{site_voltage_id}",
      {
        params: {
          path: { site_voltage_id },
        },
      },
      {
        enabled: siteVoltageEnabled,
        select(data) {
          return data.result;
        },
      },
    );
  const powerUnitEnabled = power_unit_id > 0;
  const { data: selectedPowerUnit, isSuccess: isSuccessSelectedPowerUnit } =
    $api.useQuery(
      "get",
      "/v1/pricing/power-unit/{power_unit_id}",
      {
        params: {
          path: { power_unit_id },
        },
      },
      {
        enabled: powerUnitEnabled,
        select(data) {
          return data.result;
        },
      },
    );
  const enabled = previousEnabled && siteVoltageEnabled && powerUnitEnabled;
  React.useEffect(() => {
    if (isSuccessSiteVoltage) {
      store.send({
        type: "setSiteVoltage",
        site_voltage: selectedSiteVoltage,
      });
    }
  }, [store, selectedSiteVoltage, isSuccessSiteVoltage]);
  React.useEffect(() => {
    if (isSuccessSelectedPowerUnit) {
      store.send({
        type: "setPowerUnit",
        power_unit: selectedPowerUnit,
      });
    }
  }, [store, selectedPowerUnit, isSuccessSelectedPowerUnit]);

  return {
    isSuccess: enabled
      ? previousSuccess && isSuccessSiteVoltage && isSuccessSelectedPowerUnit
      : true,
    enabled,
    selectedPumpTop,
    selectedSiteVoltage,
    selectedPowerUnit,
  };
};

export { useStep, useStepOneData, useStepTwoData };
