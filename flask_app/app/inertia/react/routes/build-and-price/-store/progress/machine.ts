import {
  powerUnitSchema,
  PowerUnitSchema,
  pumpTopSchema,
  PumpTopSchema,
  siteVoltageSchema,
  SiteVoltageSchema,
} from "@routes/build-and-price/-schemas/build";
import { assign, setup } from "xstate";

export const machine = setup({
  types: {
    context: {} as {
      step: number;
      build: {
        productName: string;
        pumpTop: PumpTopSchema;
        siteVoltage: SiteVoltageSchema;
        powerUnit: PowerUnitSchema;
      };
      pricing: {
        pumpTop: PumpTopSchema;
        siteVoltage: SiteVoltageSchema;
        powerUnit: PowerUnitSchema;
        listPrice: number;
      };
    },
    events: {} as { type: "next" | "prev" } | { type: "jump"; step?: number },
    input: {} as {
      step?: number;
      build?: {
        productName: string | undefined;
        pumpTop: PumpTopSchema | undefined;
        siteVoltage: SiteVoltageSchema | undefined;
        powerUnit: PowerUnitSchema | undefined;
      };
    },
  },
  actions: {
    setPumpTop: (_, params: { pumpTop: PumpTopSchema }) => {
      assign(({ context }) => ({
        pricing: {
          ...context.pricing,
          pumpTop: params.pumpTop,
          listPrice: ,
        },
      }));
    },
  },
  guards: {
    options: ({ context }) =>
      context.build.powerUnit.power_unit_id !== 0 &&
      context.build.siteVoltage.site_voltage_id !== 0 &&
      context.build.pumpTop.pump_top_id !== 0 &&
      context.build.productName !== "",
    build: ({ context }) => {
      console.log({ context });
      return (
        context.build.pumpTop.pump_top_id !== 0 &&
        context.build.productName !== ""
      );
    },
    pumpTop: () => true,
  },
}).createMachine({
  id: "buildMachine",
  initial: "Loading",
  context: ({ input }) => ({
    step: input.step ?? 1,
    build: {
      productName: input.build?.productName ?? "",
      pumpTop: input.build?.pumpTop ?? pumpTopSchema.parse({}),
      siteVoltage: input.build?.siteVoltage ?? siteVoltageSchema.parse({}),
      powerUnit: input.build?.powerUnit ?? powerUnitSchema.parse({}),
    },
  }),
  states: {
    Loading: {
      always: [
        { target: "Options", guard: "options" },
        { target: "Build", guard: "build" },
        { target: "PumpTop", guard: "pumpTop" },
      ],
    },
    PumpTop: {
      meta: {
        step: 1,
      },
      on: {
        next: {
          target: "Build",
        },
      },
    },
    Build: {
      meta: {
        step: 2,
      },
      on: {
        next: {
          target: "Options",
        },
      },
    },
    Options: {
      meta: {
        step: 3,
      },
      on: {
        next: {
          target: "Options",
        },
      },
    },
  },
});
