import { createFileRoute } from "@tanstack/react-router";
import { <PERSON> } from "@tanstack/react-router";
import { CostByModelChart } from "@/components/service-dashboard/cost-by-model-chart";
import { CostTrendsChart } from "@/components/service-dashboard/cost-trends-chart";
import { HighCostUnitsTable } from "@/components/service-dashboard/high-cost-units-table";
import { StructureWorkOrdersTable } from "@/components/service-dashboard/structure-work-orders-table";
import { WorkOrderPartsTable } from "@/components/service-dashboard/work-order-parts-table";
import { DrillDownBreadcrumb } from "@/components/service-dashboard/drill-down-breadcrumb";
export const Route = createFileRoute("/services/overview")({
  component: ServiceOverview,
});

function ServiceOverview() {
  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="px-4 lg:px-6">
              {/* Page Header */}
              <div className="mb-6 flex items-center justify-between">
                <h1 className="text-2xl font-bold">Service Cost Analysis</h1>
                <Link
                  to="/"
                  className="rounded bg-gray-100 px-4 py-2 text-sm font-medium text-gray-800 hover:bg-gray-200"
                >
                  Back to Dashboards
                </Link>
              </div>


              {/* Service Cost Charts */}
              <div className="grid auto-rows-min gap-4 md:grid-cols-2 mb-6">
                <CostByModelChart />
                <CostTrendsChart />
              </div>


              {/* High-Cost Units Table */}
              <div className="mb-6">
                <HighCostUnitsTable />
              </div>
              
              {/* Drill-Down Tables with Visual Hierarchy */}
              <div className="space-y-6 pb-20">
                <div className="pl-4 border-l-4 border-blue-200">
                  <StructureWorkOrdersTable />
                </div>
                <div className="pl-8 border-l-4 border-green-200">
                  <WorkOrderPartsTable />
                </div>
              </div>
          </div>
        </div>
      </div>
      
      {/* Floating Drill-Down Breadcrumb */}
      <DrillDownBreadcrumb />
    </div>
  );
}
